package com.ideal.script.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.ideal.script.common.util.BatchDataUtil;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.mapper.TaskIpsMapper;
import com.ideal.script.model.dto.AgentInfoDto;
import com.ideal.script.model.dto.ScriptExecAuditDto;
import com.ideal.script.model.dto.TaskDto;
import com.ideal.script.model.dto.TaskIpsDto;
import com.ideal.script.model.entity.AgentInfo;
import com.ideal.script.model.entity.TaskIps;
import com.ideal.script.service.IAgentInfoService;
import org.apache.ibatis.session.SqlSession;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TaskIpsServiceImplTest {

    @Mock
    private TaskIpsMapper mockTaskIpsMapper;
    @Mock
    private IAgentInfoService mockAgentInfoService;
    @Mock
    private BatchDataUtil mockBatchDataUtil;

    private TaskIpsServiceImpl taskIpsServiceImplUnderTest;

    @BeforeEach
    void setUp() throws Exception {
        taskIpsServiceImplUnderTest = new TaskIpsServiceImpl(mockTaskIpsMapper, mockAgentInfoService,
                mockBatchDataUtil);
    }

    @Test
    void testSelectTaskIpsById() {
        // Setup
        // Configure TaskIpsMapper.selectTaskIpsById(...).
        final TaskIps taskIps = new TaskIps();
        taskIps.setId(0L);
        taskIps.setScriptTaskId(0L);
        taskIps.setScriptAgentinfoId(0L);
        taskIps.setExecUserName("execUserName");
        taskIps.setAlreadyimpFlag(0);
        taskIps.setStartType(0);
        when(mockTaskIpsMapper.selectTaskIpsById(0L)).thenReturn(taskIps);

        // Run the test
        final TaskIpsDto result = taskIpsServiceImplUnderTest.selectTaskIpsById(0L);
        assertNotNull(result);
        // Verify the results
    }

    @Test
    void testSelectTaskIpsList() {
        // Setup
        final TaskIpsDto taskIpsDto = new TaskIpsDto();
        taskIpsDto.setId(0L);
        taskIpsDto.setScriptTaskId(0L);
        taskIpsDto.setSysmComputerGroupId(0L);
        taskIpsDto.setScriptAgentinfoId(0L);
        taskIpsDto.setExecUserName("execUserName");

        // Configure TaskIpsMapper.selectTaskIpsList(...).
        final TaskIps taskIps1 = new TaskIps();
        taskIps1.setId(0L);
        taskIps1.setScriptTaskId(0L);
        taskIps1.setScriptAgentinfoId(0L);
        taskIps1.setExecUserName("execUserName");
        taskIps1.setAlreadyimpFlag(0);
        taskIps1.setStartType(0);
        Page<TaskIps> page =new Page<>();
        page.add(taskIps1);
        when(mockTaskIpsMapper.selectTaskIpsList(any(TaskIps.class))).thenReturn(page);

        // Run the test
        final PageInfo<TaskIpsDto> result = taskIpsServiceImplUnderTest.selectTaskIpsList(taskIpsDto, 0, 0);
        assertNotNull(result);
        // Verify the results
    }


    @Test
    void testInsertTaskIps() {
        // Setup
        final TaskIpsDto taskIpsDto = new TaskIpsDto();
        taskIpsDto.setId(0L);
        taskIpsDto.setScriptTaskId(0L);
        taskIpsDto.setSysmComputerGroupId(0L);
        taskIpsDto.setScriptAgentinfoId(0L);
        taskIpsDto.setExecUserName("execUserName");

        when(mockTaskIpsMapper.insertTaskIps(any(TaskIps.class))).thenReturn(0);

        // Run the test
        final int result = taskIpsServiceImplUnderTest.insertTaskIps(taskIpsDto);

        // Verify the results
        assertThat(result).isZero();
    }

    @Test
    void testUpdateTaskIps() {
        // Setup
        final TaskIpsDto taskIpsDto = new TaskIpsDto();
        taskIpsDto.setId(0L);
        taskIpsDto.setScriptTaskId(0L);
        taskIpsDto.setSysmComputerGroupId(0L);
        taskIpsDto.setScriptAgentinfoId(0L);
        taskIpsDto.setExecUserName("execUserName");

        when(mockTaskIpsMapper.updateTaskIps(any(TaskIps.class))).thenReturn(0);

        // Run the test
        final int result = taskIpsServiceImplUnderTest.updateTaskIps(taskIpsDto);

        // Verify the results
        assertThat(result).isZero();
    }

    @Test
    void testDeleteTaskIpsByIds() {
        // Setup
        when(mockTaskIpsMapper.deleteTaskIpsByIds(any(Long[].class))).thenReturn(0);

        // Run the test
        final int result = taskIpsServiceImplUnderTest.deleteTaskIpsByIds(new Long[]{0L});

        // Verify the results
        assertThat(result).isZero();
    }

    @Test
    void testDeleteTaskIpsById() {
        // Setup
        when(mockTaskIpsMapper.deleteTaskIpsById(0L)).thenReturn(0);

        // Run the test
        final int result = taskIpsServiceImplUnderTest.deleteTaskIpsById(0L);

        // Verify the results
        assertThat(result).isZero();
    }

    @Test
    void testSaveTaskIps() throws Exception {
        // Setup
        final ScriptExecAuditDto scriptExecAuditDto = new ScriptExecAuditDto();
        scriptExecAuditDto.setExecuser("execuser");
        final AgentInfoDto agentInfoDto = new AgentInfoDto();
        agentInfoDto.setId(0L);
        agentInfoDto.setAgentIp("agentIp");
        agentInfoDto.setAgentPort(0);
        agentInfoDto.setExecUserName("execUserName");
        scriptExecAuditDto.setChosedAgentUsers(Collections.singletonList(agentInfoDto));

        final TaskDto taskInfo = new TaskDto();
        taskInfo.setScriptTaskSource(0);
        taskInfo.setStartType(0);
        taskInfo.setReadyToExecute(0);
        taskInfo.setId(0L);
        taskInfo.setSrcScriptUuid("srcScriptUuid");

        final SqlSession sqlSession = mock(SqlSession.class);
        when(mockAgentInfoService.checkAgentInfoExists("agentIp", 0L)).thenReturn(false);

        // Run the test
        taskIpsServiceImplUnderTest.saveTaskIps(scriptExecAuditDto, taskInfo, sqlSession);

        // Verify the results
        // Confirm IAgentInfoService.insertAgentInfo(...).
        final AgentInfoDto agentInfoDto1 = new AgentInfoDto();
        agentInfoDto1.setId(0L);
        agentInfoDto1.setSysmAgentInfoId(0L);
        agentInfoDto1.setAgentIp("agentIp");
        agentInfoDto1.setAgentPort(0);
        agentInfoDto1.setExecUserName("execUserName");
        verify(mockAgentInfoService).insertAgentInfo(agentInfoDto1);

        // Confirm BatchDataUtil.batchData(...).
        final TaskIps taskIps = new TaskIps();
        taskIps.setId(0L);
        taskIps.setScriptTaskId(0L);
        taskIps.setScriptAgentinfoId(0L);
        taskIps.setExecUserName("execUserName");
        taskIps.setAlreadyimpFlag(0);
        taskIps.setStartType(0);
        final List<TaskIps> listEntity = Collections.singletonList(taskIps);
        assertNotNull(listEntity);
    }

    @Test
    void testSaveTaskIps_IAgentInfoServiceCheckAgentInfoExistsReturnsTrue() throws Exception {
        // Setup
        final ScriptExecAuditDto scriptExecAuditDto = new ScriptExecAuditDto();
        scriptExecAuditDto.setExecuser("execuser");
        final AgentInfoDto agentInfoDto = new AgentInfoDto();
        agentInfoDto.setId(0L);
        agentInfoDto.setAgentIp("agentIp");
        agentInfoDto.setAgentPort(0);
        agentInfoDto.setExecUserName("execUserName");
        scriptExecAuditDto.setChosedAgentUsers(Collections.singletonList(agentInfoDto));

        final TaskDto taskInfo = new TaskDto();
        taskInfo.setScriptTaskSource(0);
        taskInfo.setStartType(0);
        taskInfo.setReadyToExecute(0);
        taskInfo.setId(0L);
        taskInfo.setSrcScriptUuid("srcScriptUuid");

        final SqlSession sqlSession = mock(SqlSession.class);;
        when(mockAgentInfoService.checkAgentInfoExists("agentIp", 0L)).thenReturn(true);

        // Configure IAgentInfoService.selectAgentInfoByIpAndPort(...).
        final AgentInfo agentInfo = new AgentInfo();
        agentInfo.setId(0L);
        agentInfo.setSysmAgentInfoId(0L);
        agentInfo.setAgentIp("agentIp");
        agentInfo.setAgentName("agentName");
        agentInfo.setAgentPort(0);
        final AgentInfoDto dto = new AgentInfoDto();
        dto.setId(0L);
        dto.setSysmAgentInfoId(0L);
        dto.setAgentIp("agentIp");
        dto.setAgentPort(0);
        dto.setExecUserName("execUserName");
        when(mockAgentInfoService.selectAgentInfoByIpAndPort(dto)).thenReturn(agentInfo);

        // Run the test
        taskIpsServiceImplUnderTest.saveTaskIps(scriptExecAuditDto, taskInfo, sqlSession);

        // Verify the results
        // Confirm BatchDataUtil.batchData(...).
        final TaskIps taskIps = new TaskIps();
        taskIps.setId(0L);
        taskIps.setScriptTaskId(0L);
        taskIps.setScriptAgentinfoId(0L);
        taskIps.setExecUserName("execUserName");
        taskIps.setAlreadyimpFlag(0);
        taskIps.setStartType(0);
        final List<TaskIps> listEntity = Collections.singletonList(taskIps);
        assertNotNull(listEntity);

    }

    @Test
    void testSaveTaskIps_IAgentInfoServiceSelectAgentInfoByIpAndPortThrowsScriptException() throws Exception {
        // Setup
        final ScriptExecAuditDto scriptExecAuditDto = new ScriptExecAuditDto();
        scriptExecAuditDto.setExecuser("execuser");
        final AgentInfoDto agentInfoDto = new AgentInfoDto();
        agentInfoDto.setId(0L);
        agentInfoDto.setAgentIp("agentIp");
        agentInfoDto.setAgentPort(0);
        agentInfoDto.setExecUserName("execUserName");
        scriptExecAuditDto.setChosedAgentUsers(Collections.singletonList(agentInfoDto));

        final TaskDto taskInfo = new TaskDto();
        taskInfo.setScriptTaskSource(0);
        taskInfo.setStartType(0);
        taskInfo.setReadyToExecute(0);
        taskInfo.setId(0L);
        taskInfo.setSrcScriptUuid("srcScriptUuid");

        final SqlSession sqlSession = mock(SqlSession.class);;
        when(mockAgentInfoService.checkAgentInfoExists("agentIp", 0L)).thenReturn(true);

        // Configure IAgentInfoService.selectAgentInfoByIpAndPort(...).
        final AgentInfoDto dto = new AgentInfoDto();
        dto.setId(0L);
        dto.setSysmAgentInfoId(0L);
        dto.setAgentIp("agentIp");
        dto.setAgentPort(0);
        dto.setExecUserName("execUserName");
        when(mockAgentInfoService.selectAgentInfoByIpAndPort(dto)).thenThrow(ScriptException.class);

        // Run the test
        assertThatThrownBy(
                () -> taskIpsServiceImplUnderTest.saveTaskIps(scriptExecAuditDto, taskInfo, sqlSession))
                .isInstanceOf(ScriptException.class);
    }

    @Test
    void testSaveTaskIps_batchData_scriptException() throws Exception {
        // Setup
        final ScriptExecAuditDto scriptExecAuditDto = new ScriptExecAuditDto();
        scriptExecAuditDto.setExecuser("execuser");
        final AgentInfoDto agentInfoDto = new AgentInfoDto();
        agentInfoDto.setId(0L);
        agentInfoDto.setAgentIp("agentIp");
        agentInfoDto.setAgentPort(0);
        agentInfoDto.setExecUserName("execUserName");
        scriptExecAuditDto.setChosedAgentUsers(Collections.singletonList(agentInfoDto));

        final TaskDto taskInfo = new TaskDto();
        taskInfo.setScriptTaskSource(0);
        taskInfo.setStartType(0);
        taskInfo.setReadyToExecute(0);
        taskInfo.setId(0L);
        taskInfo.setSrcScriptUuid("srcScriptUuid");

        final SqlSession sqlSession = mock(SqlSession.class);
        when(mockAgentInfoService.checkAgentInfoExists("agentIp", 0L)).thenReturn(false);

        // Run the test
        doThrow(new ScriptException("111")).when(mockBatchDataUtil)
                .batchData(any(Class.class), anyList(), any(Class.class), anyString(), any(SqlSession.class), any());
        assertThrows(ScriptException.class,()->{
            taskIpsServiceImplUnderTest.saveTaskIps(scriptExecAuditDto, taskInfo, sqlSession);
        });

        // Verify the results
        // Confirm IAgentInfoService.insertAgentInfo(...).
        final AgentInfoDto agentInfoDto1 = new AgentInfoDto();
        agentInfoDto1.setId(0L);
        agentInfoDto1.setSysmAgentInfoId(0L);
        agentInfoDto1.setAgentIp("agentIp");
        agentInfoDto1.setAgentPort(0);
        agentInfoDto1.setExecUserName("execUserName");
        verify(mockAgentInfoService).insertAgentInfo(agentInfoDto1);

        // Confirm BatchDataUtil.batchData(...).
        final TaskIps taskIps = new TaskIps();
        taskIps.setId(0L);
        taskIps.setScriptTaskId(0L);
        taskIps.setScriptAgentinfoId(0L);
        taskIps.setExecUserName("execUserName");
        taskIps.setAlreadyimpFlag(0);
        taskIps.setStartType(0);
        final List<TaskIps> listEntity = Collections.singletonList(taskIps);
        assertNotNull(listEntity);
    }

}
