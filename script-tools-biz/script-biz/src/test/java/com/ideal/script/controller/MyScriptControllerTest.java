package com.ideal.script.controller;

import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.sc.util.ValidationUtils;
import com.ideal.script.common.constant.Constants;
import com.ideal.script.common.constant.enums.ValidationMessages;
import com.ideal.script.dto.PublishDto;
import com.ideal.script.dto.ScriptInfoApiDto;
import com.ideal.script.dto.ScriptInfoDto;
import com.ideal.script.dto.ScriptInfoQueryDto;
import com.ideal.script.dto.ScriptVersionDto;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.model.bean.ExecutorValidation;
import com.ideal.script.model.dto.CreatorTransferDto;
import com.ideal.script.model.dto.ParameterValidationResultDto;
import com.ideal.script.model.dto.ScriptDeleteDto;
import com.ideal.script.model.dto.ScriptValidationResultDto;
import com.ideal.script.model.dto.ScriptVersionInfoDto;
import com.ideal.script.model.dto.ValidationResultDto;
import com.ideal.script.service.IMyScriptService;
import com.ideal.script.service.IReleaseMediaService;
import com.ideal.system.common.component.model.CurrentUser;
import com.ideal.system.dto.UserInfoApiDto;
import com.ideal.system.dto.UserInfoQueryDto;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.redisson.api.RScoredSortedSet;
import org.redisson.api.RedissonClient;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Stream;

import static com.ideal.sc.util.ValidationUtils.RESPONSE_VALIDATE_CODE;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class MyScriptControllerTest {

    @Mock
    private IMyScriptService myScriptService;

    @Mock
    private IReleaseMediaService releaseMediaService;

    @Mock
    private RedissonClient redissonClient;

    @Mock
    private RScoredSortedSet<Object> zSet;

    @InjectMocks
    private MyScriptController myScriptController;

    private ScriptInfoDto scriptInfoDto;
    private PublishDto publishDto;
    private TableQueryDto<ScriptInfoQueryDto> tableQueryDto;
    private ScriptInfoQueryDto scriptInfoQueryDto;
    private ScriptDeleteDto scriptDeleteDto;
    private CreatorTransferDto creatorTransferDto;

    private MockedStatic<ValidationUtils> validationUtilsMock;

    @BeforeEach
    void setUp() {
        // Mock ValidationUtils static method
        validationUtilsMock = mockStatic(ValidationUtils.class);
        validationUtilsMock.when(() -> ValidationUtils.customFailResult(anyString(), anyString())).thenAnswer(invocation -> R.fail( RESPONSE_VALIDATE_CODE, invocation.getArgument(0), invocation.getArgument(1)));
        scriptInfoDto = new ScriptInfoDto();
        scriptInfoDto.setId(1L);
        scriptInfoDto.setScriptNameZh("测试脚本");
        
        ScriptVersionDto scriptVersionDto = new ScriptVersionDto();
        scriptVersionDto.setVersion("1.0");
        scriptInfoDto.setScriptVersionDto(scriptVersionDto);

        publishDto = new PublishDto();
        publishDto.setIds(new Long[]{1L});

        scriptInfoQueryDto = new ScriptInfoQueryDto();
        scriptInfoQueryDto.setPageNum(1);
        scriptInfoQueryDto.setPageSize(10);

        tableQueryDto = new TableQueryDto<>();
        tableQueryDto.setQueryParam(scriptInfoQueryDto);

        scriptDeleteDto = new ScriptDeleteDto();
        scriptDeleteDto.setIds(new Long[]{1L});

        creatorTransferDto = new CreatorTransferDto();
        UserInfoApiDto userInfoApiDto = new UserInfoApiDto();
        userInfoApiDto.setId(1L);
        userInfoApiDto.setLoginName("zs");
        userInfoApiDto.setFullName("张三");
        userInfoApiDto.setOrgCode("100000000#");
        creatorTransferDto.setUserInfoApiDto(userInfoApiDto);
        creatorTransferDto.setInfoUuids(Collections.singletonList("uuid1"));
    }

    @AfterEach
    void tearDown() {
        validationUtilsMock.close();
    }

    @Test
    @DisplayName("测试保存脚本 - 成功")
    void testSaveMyScript_Success() throws Exception {
        // Arrange
        ValidationResultDto validationResultDto = new ValidationResultDto();
        doReturn(false).when(myScriptService).paramsNameExistCheck(scriptInfoDto);
        doReturn(validationResultDto).when(myScriptService).saveScript(scriptInfoDto);

        // Act
        R<Object> result = myScriptController.saveMyScript(scriptInfoDto);

        // Assert
        assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, result.getCode());
        verify(myScriptService).saveScript(scriptInfoDto);
    }

    @Test
    @DisplayName("测试保存脚本 - 参数验证不通过")
    void testSaveMyScript_ParameterValidationFailed() throws Exception {
        // Arrange
        ValidationResultDto validationResultDto = new ValidationResultDto();
        ParameterValidationResultDto parameterValidationResultDto = new ParameterValidationResultDto();
        parameterValidationResultDto.setLine(1);
        validationResultDto.setParameterValidationResultDto(parameterValidationResultDto);
        
        doReturn(false).when(myScriptService).paramsNameExistCheck(scriptInfoDto);
        doReturn(validationResultDto).when(myScriptService).saveScript(scriptInfoDto);

        // Act
        R<Object> result = myScriptController.saveMyScript(scriptInfoDto);

        // Assert
        assertEquals(Constants.REPONSE_STATUS_VALIDATA_CODE, result.getCode());
        verify(myScriptService).saveScript(scriptInfoDto);
    }

    @Test
    @DisplayName("测试保存脚本 - 存在屏蔽关键命令")
    void testSaveMyScript_BlockedCommand() throws Exception {
        // Arrange
        ValidationResultDto validationResultDto = new ValidationResultDto();
        ScriptValidationResultDto blockedResult = new ScriptValidationResultDto();
        blockedResult.setType(1);
        validationResultDto.setScriptValidationResultDtoList(Collections.singletonList(blockedResult));
        
        doReturn(false).when(myScriptService).paramsNameExistCheck(scriptInfoDto);
        doReturn(validationResultDto).when(myScriptService).saveScript(scriptInfoDto);

        // Act
        R<Object> result = myScriptController.saveMyScript(scriptInfoDto);

        // Assert
        assertEquals(Constants.REPONSE_STATUS_VALIDATA_CODE, result.getCode());
        verify(myScriptService).saveScript(scriptInfoDto);
    }

    @Test
    @DisplayName("测试保存脚本 - 存在提醒关键命令")
    void testSaveMyScript_WarningCommand() throws Exception {
        // Arrange
        ValidationResultDto validationResultDto = new ValidationResultDto();
        ScriptValidationResultDto warningResult = new ScriptValidationResultDto();
        warningResult.setType(2);
        validationResultDto.setScriptValidationResultDtoList(Collections.singletonList(warningResult));
        
        doReturn(false).when(myScriptService).paramsNameExistCheck(scriptInfoDto);
        doReturn(validationResultDto).when(myScriptService).saveScript(scriptInfoDto);

        // Act
        R<Object> result = myScriptController.saveMyScript(scriptInfoDto);

        // Assert
        assertEquals(Constants.REPONSE_STATUS_VALIDATA_CODE, result.getCode());
        verify(myScriptService).saveScript(scriptInfoDto);
    }

    @Test
    @DisplayName("测试保存脚本 - 更新草稿")
    void testSaveMyScript_UpdateDraft() throws Exception {
        // Arrange
        scriptInfoDto.getScriptVersionDto().setVersion(null);
        ValidationResultDto validationResultDto = new ValidationResultDto();
        
        doReturn(false).when(myScriptService).paramsNameExistCheck(scriptInfoDto);
        doReturn(validationResultDto).when(myScriptService).updateMyScript(scriptInfoDto);

        // Act
        R<Object> result = myScriptController.saveMyScript(scriptInfoDto);

        // Assert
        assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, result.getCode());
        verify(myScriptService).updateMyScript(scriptInfoDto);
        verify(myScriptService, never()).saveScript(any());
    }

    @Test
    @DisplayName("测试保存脚本 - 抛出异常")
    void testSaveMyScript_Exception() throws Exception {
        // Arrange
        doReturn(false).when(myScriptService).paramsNameExistCheck(scriptInfoDto);
        doThrow(new ScriptException("保存失败")).when(myScriptService).saveScript(scriptInfoDto);

        // Act
        R<Object> result = myScriptController.saveMyScript(scriptInfoDto);

        // Assert
        assertEquals(Constants.REPONSE_STATUS_VALIDATA_CODE, result.getCode());
        verify(myScriptService).saveScript(scriptInfoDto);
    }

    @Test
    @DisplayName("测试保存脚本 - 参数名称重复")
    void testSaveMyScript_ParameterNameExist() throws Exception {
        // Arrange
        doReturn(true).when(myScriptService).paramsNameExistCheck(scriptInfoDto);

        // Act
        R<Object> result = myScriptController.saveMyScript(scriptInfoDto);

        // Assert
        assertEquals(Constants.REPONSE_STATUS_VALIDATA_CODE, result.getCode());
        verify(myScriptService, never()).saveScript(any());
    }

    @Test
    @DisplayName("测试更新脚本 - 成功")
    void testUpdateMyScript_Success() throws Exception {
        // Arrange
        ValidationResultDto validationResultDto = new ValidationResultDto();
        doReturn(false).when(myScriptService).paramsNameExistCheck(scriptInfoDto);
        doReturn(validationResultDto).when(myScriptService).updateMyScript(scriptInfoDto);

        // Act
        R<Object> result = myScriptController.updateMyScript(scriptInfoDto);

        // Assert
        assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, result.getCode());
        verify(myScriptService).updateMyScript(scriptInfoDto);
    }

    @Test
    @DisplayName("测试更新脚本 - 参数验证不通过")
    void testUpdateMyScript_ParameterValidationFailed() throws Exception {
        // Arrange
        ValidationResultDto validationResultDto = new ValidationResultDto();
        ParameterValidationResultDto parameterValidationResultDto = new ParameterValidationResultDto();
        parameterValidationResultDto.setLine(1);
        validationResultDto.setParameterValidationResultDto(parameterValidationResultDto);
        
        doReturn(false).when(myScriptService).paramsNameExistCheck(scriptInfoDto);
        doReturn(validationResultDto).when(myScriptService).updateMyScript(scriptInfoDto);

        // Act
        R<Object> result = myScriptController.updateMyScript(scriptInfoDto);

        // Assert
        assertEquals(Constants.REPONSE_STATUS_VALIDATA_CODE, result.getCode());
        verify(myScriptService).updateMyScript(scriptInfoDto);
    }

    @Test
    @DisplayName("测试更新脚本 - 存在屏蔽关键命令")
    void testUpdateMyScript_BlockedCommand() throws Exception {
        // Arrange
        ValidationResultDto validationResultDto = new ValidationResultDto();
        ScriptValidationResultDto blockedResult = new ScriptValidationResultDto();
        blockedResult.setType(1);
        validationResultDto.setScriptValidationResultDtoList(Collections.singletonList(blockedResult));
        
        doReturn(false).when(myScriptService).paramsNameExistCheck(scriptInfoDto);
        doReturn(validationResultDto).when(myScriptService).updateMyScript(scriptInfoDto);

        // Act
        R<Object> result = myScriptController.updateMyScript(scriptInfoDto);

        // Assert
        assertEquals(Constants.REPONSE_STATUS_VALIDATA_CODE, result.getCode());
        verify(myScriptService).updateMyScript(scriptInfoDto);
    }

    @Test
    @DisplayName("测试更新脚本 - 存在提醒关键命令")
    void testUpdateMyScript_WarningCommand() throws Exception {
        // Arrange
        ValidationResultDto validationResultDto = new ValidationResultDto();
        ScriptValidationResultDto warningResult = new ScriptValidationResultDto();
        warningResult.setType(2);
        validationResultDto.setScriptValidationResultDtoList(Collections.singletonList(warningResult));
        
        doReturn(false).when(myScriptService).paramsNameExistCheck(scriptInfoDto);
        doReturn(validationResultDto).when(myScriptService).updateMyScript(scriptInfoDto);

        // Act
        R<Object> result = myScriptController.updateMyScript(scriptInfoDto);

        // Assert
        assertEquals(Constants.REPONSE_STATUS_VALIDATA_CODE, result.getCode());
        verify(myScriptService).updateMyScript(scriptInfoDto);
    }

    @Test
    @DisplayName("测试更新脚本 - 抛出异常")
    void testUpdateMyScript_Exception() throws Exception {
        // Arrange
        doReturn(false).when(myScriptService).paramsNameExistCheck(scriptInfoDto);
        doThrow(new ScriptException("更新失败")).when(myScriptService).updateMyScript(scriptInfoDto);

        // Act
        R<Object> result = myScriptController.updateMyScript(scriptInfoDto);

        // Assert
        assertEquals(Constants.REPONSE_STATUS_VALIDATA_CODE, result.getCode());
        verify(myScriptService).updateMyScript(scriptInfoDto);
    }

    @Test
    @DisplayName("测试更新脚本 - 参数名称重复")
    void testUpdateMyScript_ParameterNameExist() throws Exception {
        // Arrange
        doReturn(true).when(myScriptService).paramsNameExistCheck(scriptInfoDto);

        // Act
        R<Object> result = myScriptController.updateMyScript(scriptInfoDto);

        // Assert
        assertEquals(Constants.REPONSE_STATUS_VALIDATA_CODE, result.getCode());
        verify(myScriptService, never()).updateMyScript(any());
    }

    @Test
    @DisplayName("测试获取脚本列表 - 成功")
    void testListMyScript_Success() {
        // Arrange
        PageInfo<ScriptInfoApiDto> pageInfo = new PageInfo<>();
        
        // Use any() matcher to avoid strict argument matching
        doReturn(pageInfo).when(myScriptService).selectScriptPageList(any(ScriptInfoQueryDto.class));

        // Act
        R<PageInfo<ScriptInfoApiDto>> result = myScriptController.listMyScript(tableQueryDto);

        // Assert
        assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, result.getCode());
        verify(myScriptService).selectScriptPageList(any(ScriptInfoQueryDto.class));
    }

    @ParameterizedTest
    @MethodSource("deleteMyScriptTestCases")
    @DisplayName("测试删除脚本 - 参数化测试")
    void testDeleteMyScript_Parameterized(boolean throwException, String expectedCode) throws Exception {
        // Arrange
        if (throwException) {
            doThrow(new ScriptException("删除失败")).when(myScriptService).deleteMyScript(scriptDeleteDto, false);
        } else {
            doNothing().when(myScriptService).deleteMyScript(scriptDeleteDto, false);
        }

        // Act
        R<Object> result = myScriptController.deleteMyScript(scriptDeleteDto);

        // Assert
        assertEquals(expectedCode, result.getCode());
        verify(myScriptService).deleteMyScript(scriptDeleteDto, false);
    }

    static Stream<Arguments> deleteMyScriptTestCases() {
        return Stream.of(
            Arguments.of(false, Constants.REPONSE_STATUS_SUSSCESS_CODE), // 正常删除
            Arguments.of(true, Constants.REPONSE_STATUS_VALIDATA_CODE)   // 删除失败
        );
    }

    @ParameterizedTest
    @MethodSource("publishScriptTestCases")
    @DisplayName("测试发布脚本 - 参数化测试")
    void testPublishScript_Parameterized(Long[] ids, boolean throwException, String expectedCode, boolean shouldCallService) throws Exception {
        // Arrange
        PublishDto testDto = new PublishDto();
        testDto.setIds(ids);
        
        if (shouldCallService) {
            if (throwException) {
                doThrow(new ScriptException("发布失败")).when(myScriptService).publishScript(testDto);
            } else {
                doNothing().when(myScriptService).publishScript(testDto);
            }
        }

        // Act
        R<Object> result = myScriptController.publishScript(testDto);

        // Assert
        assertEquals(expectedCode, result.getCode());
        if (shouldCallService) {
            verify(myScriptService).publishScript(testDto);
        } else {
            verify(myScriptService, never()).publishScript(any());
        }
    }

    static Stream<Arguments> publishScriptTestCases() {
        return Stream.of(
            Arguments.of(new Long[]{1L}, false, Constants.REPONSE_STATUS_SUSSCESS_CODE, true),  // 正常发布
            Arguments.of(new Long[]{1L}, true, Constants.REPONSE_STATUS_VALIDATA_CODE, true),   // 发布失败
            Arguments.of(null, false, Constants.REPONSE_STATUS_VALIDATA_CODE, false),           // 空ID
            Arguments.of(new Long[]{}, false, Constants.REPONSE_STATUS_VALIDATA_CODE, false)    // 空数组
        );
    }

    @Test
    @DisplayName("测试创建者转移 - 成功")
    void testCreatorTransfer_Success() {
        // Arrange
        doNothing().when(myScriptService).creatorTransfer(creatorTransferDto);

        // Act
        R<Object> result = myScriptController.creatorTransfer(creatorTransferDto);

        // Assert
        assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, result.getCode());
        assertEquals("创建者转移成功", result.getMessage());
        verify(myScriptService).creatorTransfer(creatorTransferDto);
    }

    @Test
    @DisplayName("测试创建者转移 - 服务抛出异常")
    void testCreatorTransfer_ServiceException()  {
        // Arrange
        doThrow(new RuntimeException("转移失败")).when(myScriptService).creatorTransfer(creatorTransferDto);

        // Act
        R<Object> result = myScriptController.creatorTransfer(creatorTransferDto);

        // Assert
        assertEquals(Constants.REPONSE_STATUS_FAIL_CODE, result.getCode());
        assertEquals("创建者转移失败", result.getMessage());
        verify(myScriptService).creatorTransfer(creatorTransferDto);
    }

    @Test
    @DisplayName("测试获取脚本详情 - 成功")
    void testGetScriptDetail_Success() throws Exception {
        // Arrange
        ScriptInfoDto scriptInfoDto = new ScriptInfoDto();
        doReturn(scriptInfoDto).when(myScriptService).getScriptDetail(scriptInfoQueryDto);

        // Act
        R<ScriptInfoDto> result = myScriptController.getScriptDetail(scriptInfoQueryDto);

        // Assert
        assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, result.getCode());
        verify(myScriptService).getScriptDetail(scriptInfoQueryDto);
    }

    @Test
    @DisplayName("测试获取脚本详情 - 异常")
    void testGetScriptDetail_Exception() throws Exception {
        // Arrange
        doThrow(new ScriptException("获取详情失败")).when(myScriptService).getScriptDetail(scriptInfoQueryDto);

        // Act & Assert
        // 由于控制器方法声明了 throws ScriptException，异常会被抛出而不是被捕获处理
        // 因此我们需要验证异常确实被抛出
        try {
            myScriptController.getScriptDetail(scriptInfoQueryDto);
            // 如果没有抛出异常，测试应该失败
            assertEquals("应该抛出ScriptException", "但没有抛出异常");
        } catch (ScriptException e) {
            // 验证抛出的异常消息
            assertEquals("获取详情失败", e.getMessage());
        }

        // 验证服务方法被调用
        verify(myScriptService).getScriptDetail(scriptInfoQueryDto);
    }

//    @ParameterizedTest
//    @MethodSource("getDoubleCheckScriptFlagTestCases")
//    @DisplayName("测试获取双人复核标志 - 参数化测试")
//    void testGetDoubleCheckScriptFlag_Parameterized(Long[] scriptIds, boolean flag, String expectedCode)  {
//        // Arrange
//        if (scriptIds != null && scriptIds.length > 0) {
//            doReturn(flag).when(myScriptService).getDoubleCheckScriptFlag(scriptIds);
//        }
//
//        // Act
//        R<Object> result = myScriptController.publishScript(scriptIds);
//
//        // Assert
//        assertEquals(expectedCode, result.getCode());
//        if (scriptIds != null && scriptIds.length > 0) {
//            verify(myScriptService).getDoubleCheckScriptFlag(scriptIds);
//        }
//    }

    static Stream<Arguments> getDoubleCheckScriptFlagTestCases() {
        return Stream.of(
            Arguments.of(new Long[]{1L}, false, Constants.REPONSE_STATUS_SUSSCESS_CODE), // 不存在发布审核打回脚本
            Arguments.of(new Long[]{1L}, true, Constants.REPONSE_STATUS_VALIDATA_CODE),  // 存在发布审核打回脚本
            Arguments.of(null, false, Constants.REPONSE_STATUS_SUSSCESS_CODE),           // 空ID
            Arguments.of(new Long[]{}, false, Constants.REPONSE_STATUS_SUSSCESS_CODE)    // 空数组
        );
    }

    @ParameterizedTest
    @MethodSource("scriptDownloadTestCases")
    @DisplayName("测试脚本下载 - 参数化测试")
    void testScriptDownload_Parameterized(Long[] ids, boolean downloadResult) {
        // Arrange
        HttpServletResponse response = mock(HttpServletResponse.class);
        doReturn(downloadResult).when(myScriptService).downloadScript(ids, response);

        // Act
        myScriptController.scriptDownload(ids, response);

        // Assert
        verify(myScriptService).downloadScript(ids, response);
    }

    static Stream<Arguments> scriptDownloadTestCases() {
        return Stream.of(
            Arguments.of(new Long[]{1L}, true),  // 下载成功
            Arguments.of(new Long[]{1L}, false), // 下载失败
            Arguments.of(null, false),           // 空ID
            Arguments.of(new Long[]{}, false)    // 空数组
        );
    }

    @Test
    @DisplayName("测试获取所有脚本版本 - 成功")
    void testGetScriptServiceVersionListForAllScript_Success() {
        // Arrange
        String serviceUuid = "uuid1";
        List<ScriptVersionInfoDto> versions = Collections.singletonList(new ScriptVersionInfoDto());
        doReturn(versions).when(myScriptService).getScriptServiceVersionListForAllScript(serviceUuid);

        // Act
        R<List<ScriptVersionInfoDto>> result = myScriptController.getScriptServiceVersionListForAllScript(serviceUuid);

        // Assert
        assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, result.getCode());
        verify(myScriptService).getScriptServiceVersionListForAllScript(serviceUuid);
    }

    @Test
    @DisplayName("测试无版本回退 - 成功")
    void testNoVersionRollBack_Success() {
        // Arrange
        Long iid = 1L;
        Long oldId = 2L;
        String uuid = "uuid1";
        String oldUuid = "uuid2";

        Map<String,Object> res = new HashMap<>();
        res.put("success",false);
        res.put("message","hasVersionRollBack error");

        doReturn(res).when(myScriptService).noVersionRollBack(iid, oldId, oldUuid, uuid);

        // Act
        R<Object> result = myScriptController.noVersionRollBack(iid, oldId, uuid, oldUuid);

        // Assert
        assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, result.getCode());
        verify(myScriptService).noVersionRollBack(iid, oldId, oldUuid, uuid);
    }

    @Test
    @DisplayName("测试有版本回退 - 成功")
    void testHasVersionRollBack_Success() {
        // Arrange
        Long iid = 1L;
        Long oldId = 2L;

        Map<String,Object> res = new HashMap<>();

        res.put("success",true);
        res.put("message","rollback success");

        doReturn(res).when(myScriptService).hasVersionRollBack(iid, oldId);

        // Act
        R<Object> result = myScriptController.hasVersionRollBack(iid, oldId);

        // Assert
        assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, result.getCode());
        verify(myScriptService).hasVersionRollBack(iid, oldId);
    }

    @Test
    @DisplayName("测试获取用户信息列表 - 成功")
    void testGetUserInfoList_Success() {
        // Arrange
        UserInfoQueryDto userInfoQueryDto = new UserInfoQueryDto();
        PageInfo<UserInfoApiDto> pageInfo = new PageInfo<>();
        doReturn(pageInfo).when(myScriptService).getUserInfoList(userInfoQueryDto);

        // Act
        R<PageInfo<UserInfoApiDto>> result = myScriptController.getUserInfoList(userInfoQueryDto);

        // Assert
        assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, result.getCode());
        verify(myScriptService).getUserInfoList(userInfoQueryDto);
    }

    @ParameterizedTest
    @MethodSource("myScriptExportReleaseMediaTestCases")
    @DisplayName("测试脚本投产介质导出 - 参数化测试")
    void testMyScriptExportReleaseMedia_Parameterized(Long[] ids, Long[] versionIds, boolean throwException) throws ScriptException {
        // Arrange
        HttpServletResponse response = mock(HttpServletResponse.class);
        doReturn(versionIds).when(myScriptService).getDefaultVersionIds(ids);
        
        if (throwException) {
            doThrow(new ScriptException("导出失败")).when(releaseMediaService).exportReleaseMedia(versionIds, response);
        } else if (versionIds != null && versionIds.length > 0) {
            doNothing().when(releaseMediaService).exportReleaseMedia(versionIds, response);
        }

        // Act
        myScriptController.myScriptExportReleaseMedia(ids, response);

        // Assert
        verify(myScriptService).getDefaultVersionIds(ids);
        if (versionIds != null && versionIds.length > 0) {
            verify(releaseMediaService).exportReleaseMedia(versionIds, response);
        } else {
            verify(releaseMediaService, never()).exportReleaseMedia(any(), any());
        }
    }

    static Stream<Arguments> myScriptExportReleaseMediaTestCases() {
        return Stream.of(
            Arguments.of(new Long[]{1L}, new Long[]{1L}, false),  // 正常导出
            Arguments.of(new Long[]{1L}, new Long[]{1L}, true),   // 导出失败
            Arguments.of(new Long[]{1L}, null, false),            // 获取版本ID失败
            Arguments.of(new Long[]{1L}, new Long[]{}, false),    // 空版本ID
            Arguments.of(null, null, false),                      // 空ID
            Arguments.of(new Long[]{}, null, false)               // 空数组
        );
    }

    @Test
    @DisplayName("测试忽略脚本修订 - 成功")
    void testIgnoreScriptRevision_Success() {
        // Arrange
        List<Long> ids = Collections.singletonList(1L);
        doNothing().when(myScriptService).ignoreScriptRevision(eq(ids), any(CurrentUser.class));

        // Act
        R<Object> result = myScriptController.ignoreScriptRevision(ids);

        // Assert
        assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, result.getCode());
        verify(myScriptService).ignoreScriptRevision(eq(ids), any(CurrentUser.class));
    }

    @ParameterizedTest
    @MethodSource("getDeleteFlagTestCases")
    @DisplayName("测试获取删除标志 - 参数化测试")
    void testGetDeleteFlag_Parameterized(Long scriptAuditRelationId, boolean flag, String expectedCode) throws ScriptException {
        // Arrange
        lenient().doReturn(flag).when(myScriptService).getDelDoubleCheckScriptFlag(scriptAuditRelationId);

        // Act
        R<Object> result = myScriptController.getDeleteFlag(scriptAuditRelationId);

        // Assert
        assertEquals(expectedCode, result.getCode());
        if(!Objects.isNull(scriptAuditRelationId)) {
            verify(myScriptService).getDelDoubleCheckScriptFlag(scriptAuditRelationId);
        }
    }

    static Stream<Arguments> getDeleteFlagTestCases() {
        return Stream.of(
            Arguments.of(1L, true, Constants.REPONSE_STATUS_VALIDATA_CODE),  // 是发布审核打回
            Arguments.of(1L, false, Constants.REPONSE_STATUS_SUSSCESS_CODE), // 不是发布审核打回
            Arguments.of(null, false, Constants.REPONSE_STATUS_VALIDATA_CODE) // 空ID
        );
    }

    @Test
    @DisplayName("测试获取执行器验证列表 - 成功")
    void testGetExecutorValidationList_Success() {
        // Arrange
        ExecutorValidation validation = new ExecutorValidation();
        doReturn(validation).when(myScriptService).getExecutorValidationList();

        // Act
        R<ExecutorValidation> result = myScriptController.getExecutorValidationList();

        // Assert
        assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, result.getCode());
        verify(myScriptService).getExecutorValidationList();
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @DisplayName("测试获取SQL展示标志 - 参数化测试")
    void testGetSqlShowFlag_Parameterized(boolean sqlShowFlag) {
        // Arrange
        doReturn(sqlShowFlag).when(myScriptService).getSqlShowFlag();

        // Act
        R<Object> result = myScriptController.getTaskCountByVersionId();

        // Assert
        assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, result.getCode());
        assertEquals(sqlShowFlag, result.getData());
        verify(myScriptService).getSqlShowFlag();
    }

    @ParameterizedTest
    @MethodSource("getLabelListTestCases")
    @DisplayName("测试获取标签列表 - 参数化测试")
    void testGetLabelList_Parameterized(boolean redisEmpty, Set<String> dbLabels, int expectedSize) {
        // Arrange
        when(redissonClient.getScoredSortedSet("script_labelZSet")).thenReturn(zSet);
        when(zSet.isEmpty()).thenReturn(redisEmpty);
        
        if (!redisEmpty) {
            when(zSet.readAll()).thenReturn(new HashSet<>(dbLabels));
        } else {
            doReturn(dbLabels).when(myScriptService).getLabelList();
        }

        // Act
        R<Set<String>> result = myScriptController.getLabelList(scriptInfoQueryDto);

        // Assert
        assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, result.getCode());
        assertEquals(expectedSize, result.getData().size());
        
        if (redisEmpty) {
            verify(myScriptService).getLabelList();
        } else {
            verify(myScriptService, never()).getLabelList();
        }
    }

    static Stream<Arguments> getLabelListTestCases() {
        Set<String> labels1 = new HashSet<>(Arrays.asList("label1", "label2"));
        Set<String> labels2 = new HashSet<>(Arrays.asList("label3", "label4"));
        
        return Stream.of(
            Arguments.of(false, Collections.unmodifiableSet(labels1), 2), // 从Redis获取
            Arguments.of(true, Collections.unmodifiableSet(labels2), 2),  // 从数据库获取
            Arguments.of(false, Collections.emptySet(), 0),               // Redis空数据
            Arguments.of(true, Collections.emptySet(), 0)                 // 数据库空数据
        );
    }

    @Test
    @DisplayName("测试处理验证结果 - 参数验证不通过")
    void testHandleValidationResult_ParameterValidationFailed() throws Exception {
        // Arrange
        ValidationResultDto validationResultDto = new ValidationResultDto();
        ParameterValidationResultDto parameterValidationResultDto = new ParameterValidationResultDto();
        parameterValidationResultDto.setLine(1);
        validationResultDto.setParameterValidationResultDto(parameterValidationResultDto);

        // Act
        R<Object> result = invokeHandleValidationResult(validationResultDto);

        // Assert
        assertEquals(Constants.REPONSE_STATUS_VALIDATA_CODE, result.getCode());
        assertEquals(ValidationMessages.PARAMETER_VALIDATION_FAILED_MESSAGE.getMessage(), result.getMessage());
    }

    @Test
    @DisplayName("测试处理验证结果 - 存在屏蔽关键命令")
    void testHandleValidationResult_BlockedCommand() throws Exception {
        // Arrange
        ValidationResultDto validationResultDto = new ValidationResultDto();
        ScriptValidationResultDto blockedResult = new ScriptValidationResultDto();
        blockedResult.setType(1);
        validationResultDto.setScriptValidationResultDtoList(Collections.singletonList(blockedResult));

        // Act
        R<Object> result = invokeHandleValidationResult(validationResultDto);

        // Assert
        assertEquals(Constants.REPONSE_STATUS_VALIDATA_CODE, result.getCode());
        assertEquals(ValidationMessages.SCRIPT_BLOCKED_MESSAGE.getMessage(), result.getMessage());
    }

    @Test
    @DisplayName("测试处理验证结果 - 存在提醒关键命令")
    void testHandleValidationResult_WarningCommand() throws Exception {
        // Arrange
        ValidationResultDto validationResultDto = new ValidationResultDto();
        ScriptValidationResultDto warningResult = new ScriptValidationResultDto();
        warningResult.setType(2);
        validationResultDto.setScriptValidationResultDtoList(Collections.singletonList(warningResult));

        // Act
        R<Object> result = invokeHandleValidationResult(validationResultDto);

        // Assert
        assertEquals(Constants.REPONSE_STATUS_VALIDATA_CODE, result.getCode());
        assertEquals(ValidationMessages.CONTAINS_KEYWORD_MESSAGE.getMessage(), result.getMessage());
    }

    @Test
    @DisplayName("测试处理验证结果 - 验证通过")
    void testHandleValidationResult_ValidationPassed() throws Exception {
        // Arrange
        ValidationResultDto validationResultDto = new ValidationResultDto();

        // Act
        R<Object> result = invokeHandleValidationResult(validationResultDto);

        // Assert
        assertNull(result);
    }

    private R<Object> invokeHandleValidationResult(ValidationResultDto res) throws Exception {
        java.lang.reflect.Method method = MyScriptController.class.getDeclaredMethod("handleValidationResult", ValidationResultDto.class);
        method.setAccessible(true);
        return (R<Object>) method.invoke(myScriptController, res);
    }

    @ParameterizedTest
    @MethodSource("getDoubleCheckScriptFlagTestCases2")
    @DisplayName("测试获取双人复核标志 - 参数化测试")
    void testGetDoubleCheckScriptFlag_Parameterized(Long[] scriptIds, boolean flag, String expectedCode, boolean shouldCallArchive) {
        // Arrange
        if (scriptIds != null && scriptIds.length > 0) {
            doReturn(flag).when(myScriptService).getDoubleCheckScriptFlag(scriptIds);
        }

        // Act
        R<Object> result = myScriptController.publishScript(scriptIds);

        // Assert
        assertEquals(expectedCode, result.getCode());
        if (shouldCallArchive) {
            verify(myScriptService).doubleCheckAutoArchive(scriptIds);
        } else {
            verify(myScriptService, never()).doubleCheckAutoArchive(any());
        }
    }

    static Stream<Arguments> getDoubleCheckScriptFlagTestCases2() {
        return Stream.of(
            Arguments.of(null, false, Constants.REPONSE_STATUS_SUSSCESS_CODE, false),          // 空ID
            Arguments.of(new Long[]{}, false, Constants.REPONSE_STATUS_SUSSCESS_CODE, false),   // 空数组
            Arguments.of(new Long[]{1L}, true, Constants.REPONSE_STATUS_SUSSCESS_CODE, true),   // 需要归档
            Arguments.of(new Long[]{1L}, false, Constants.REPONSE_STATUS_SUSSCESS_CODE, false)  // 不需要归档
        );
    }

}
