package com.ideal.script.service.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.PageDataUtil;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.mapper.AgentInfoMapper;
import com.ideal.script.model.bean.TaskBindAgentInfoBean;
import com.ideal.script.model.dto.AgentInfoDto;
import com.ideal.script.model.dto.QueryAgentInfoDto;
import com.ideal.script.model.entity.AgentInfo;
import com.ideal.script.service.IAgentInfoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * Agent基本信息-冗余平台管理Service业务层处理
 *
 * <AUTHOR>
 */
@Service("scriptAgentInfoServiceImpl")
public class AgentInfoServiceImpl implements IAgentInfoService {

    private final AgentInfoMapper agentInfoMapper;
    private static final Logger logger = LoggerFactory.getLogger(AgentInfoServiceImpl.class);

    public AgentInfoServiceImpl(AgentInfoMapper agentInfoMapper) {
        this.agentInfoMapper = agentInfoMapper;
    }

    /**
     * 查询Agent基本信息-冗余平台管理
     *
     * @param id Agent基本信息-冗余平台管理主键
     * @return Agent基本信息-冗余平台管理
     */
    @Override
    public AgentInfoDto selectAgentInfoById(Long id) {
        return BeanUtils.copy(agentInfoMapper.selectAgentInfoById(id), AgentInfoDto.class);
    }

    /**
     * 查询Agent基本信息-冗余平台管理列表
     *
     * @param agentInfoDto Agent基本信息-冗余平台管理
     * @return Agent基本信息-冗余平台管理
     */
    @Override
    public PageInfo<AgentInfoDto> selectAgentInfoList(AgentInfoDto agentInfoDto, int pageNum, int pageSize) {
        AgentInfo agentInfo = BeanUtils.copy(agentInfoDto, AgentInfo.class);
        PageMethod.startPage(pageNum, pageSize);
        List<AgentInfo> agentInfoList = agentInfoMapper.selectAgentInfoList(agentInfo);
        return PageDataUtil.toDtoPage(agentInfoList, AgentInfoDto.class);
    }

    /**
     * 新增Agent基本信息-冗余平台管理
     *
     * @param agentInfoDto Agent基本信息-冗余平台管理
     * @return 结果
     */
    @Override
    public int insertAgentInfo(AgentInfoDto agentInfoDto) {
        AgentInfo agentInfo = BeanUtils.copy(agentInfoDto, AgentInfo.class);
        int result = agentInfoMapper.insertAgentInfo(agentInfo);
        agentInfoDto.setId(agentInfo.getId());
        return result;
    }

    /**
     * 修改Agent基本信息-冗余平台管理
     *
     * @param agentInfoDto Agent基本信息-冗余平台管理
     * @return 结果
     */
    @Override
    public int updateAgentInfo(AgentInfoDto agentInfoDto) {
        AgentInfo agentInfo = BeanUtils.copy(agentInfoDto, AgentInfo.class);
        return agentInfoMapper.updateAgentInfo(agentInfo);
    }

    /**
     * 批量删除Agent基本信息-冗余平台管理
     *
     * @param ids 需要删除的Agent基本信息-冗余平台管理主键
     * @return 结果
     */
    @Override
    public int deleteAgentInfoByIds(Long[] ids) {
        return agentInfoMapper.deleteAgentInfoByIds(ids);
    }

    /**
     * 删除Agent基本信息-冗余平台管理信息
     *
     * @param id Agent基本信息-冗余平台管理主键
     * @return 结果
     */
    @Override
    public int deleteAgentInfoById(Long id) {
        return agentInfoMapper.deleteAgentInfoById(id);
    }

    /**
     * 根据任务taskId查询任务绑定的agent信息
     *
     * @return List<AgentInfoDto>
     * <AUTHOR>
     */
    @Override
    public PageInfo<AgentInfoDto> getTaskBindAgentInfo(QueryAgentInfoDto queryAgentInfoDto, Integer pageNum, Integer pageSize) {
        PageMethod.startPage(pageNum, pageSize);
        List<TaskBindAgentInfoBean> agentInfoList = new ArrayList<>();
        if (null != queryAgentInfoDto) {
            AgentInfoDto agentInfoDto = new AgentInfoDto();
            agentInfoDto.setAgentIp(queryAgentInfoDto.getAgentIp());
            agentInfoDto.setAgentName(queryAgentInfoDto.getAgentName());
            AgentInfo agentInfo = BeanUtils.copy(agentInfoDto, AgentInfo.class);
            agentInfoList = agentInfoMapper.getTaskBindAgentInfo(agentInfo, queryAgentInfoDto.getScriptTaskId(),null);
        }
        return PageDataUtil.toDtoPage(agentInfoList,AgentInfoDto.class);
    }

    /**
     * 功能描述： 检查ieai_script_agent_info表中是否已存在相同的记录(IP+端口)
     *
     * @param agentIp   agentIp
     * @param agentPort agent端口
     * @return boolean
     * <AUTHOR>
     */
    @Override
    public boolean checkAgentInfoExists(String agentIp, Long agentPort) {
        return agentInfoMapper.checkAgentInfoExists(agentIp,agentPort);
    }

    /**
     * 根据Ip、端口查询agent信息
     * @param agentInfoDto agent信息
     * @return AgentInfo
     */

    @Override
    public AgentInfo selectAgentInfoByIpAndPort(AgentInfoDto agentInfoDto) throws ScriptException {
        AgentInfo agentInfo = BeanUtils.copy(agentInfoDto, AgentInfo.class);
        List<AgentInfo> agentInfoList = agentInfoMapper.selectAgentInfoList(agentInfo);
        if (agentInfoList != null && !agentInfoList.isEmpty()) {
            return agentInfoList.get(0);
        } else {
            logger.error("未找到给定IP和端口的数据！");
            throw new ScriptException("data.not.found.for.ip.port");
        }
    }

    /**
     * 根据业务主键-脚本服务化关系表主键获取任务绑定的agent信息
     *
     * @param serviceId 业务主键
     * @return List<AgentInfoDto>
     */
    @Override
    public List<AgentInfoDto> selectAgentInfoByServiceId(Long serviceId,Long taskId) {
        List<AgentInfo> agentInfoList = agentInfoMapper.selectAgentInfoByServiceId(serviceId,taskId);
        return BeanUtils.copy(agentInfoList, AgentInfoDto.class);
    }

    /**
     * 根据任务taskId查询任务绑定的所有agent信息
     *
     * @return List<AgentInfoDto>
     * <AUTHOR>
     */
    @Override
    public PageInfo<AgentInfoDto> getTaskAllAgentInfo(QueryAgentInfoDto queryAgentInfoDto, Integer pageNum, Integer pageSize) {
        List<TaskBindAgentInfoBean> agentInfoList = new ArrayList<>();
        if (null != queryAgentInfoDto) {
            AgentInfoDto agentInfoDto = new AgentInfoDto();
            agentInfoDto.setAgentIp(queryAgentInfoDto.getAgentIp());
            agentInfoDto.setAgentName(queryAgentInfoDto.getAgentName());
            AgentInfo agentInfo = BeanUtils.copy(agentInfoDto, AgentInfo.class);
            PageMethod.startPage(pageNum, pageSize);
            agentInfoList = agentInfoMapper.getTaskBindAgentInfo(agentInfo, queryAgentInfoDto.getScriptTaskId(),"all");
        }
        return PageDataUtil.toDtoPage(agentInfoList,AgentInfoDto.class);
    }

    @Override
    public List<AgentInfo> selectAgentInfoByIpAndPort(List<AgentInfoDto> agentInfoDtoList){
        if(!agentInfoDtoList.isEmpty()){
            return agentInfoMapper.selectAgentInfoByIpAndPort(agentInfoDtoList);
        }else{
            return null;
        }
    }

}
