package com.ideal.script.model.dto;


import com.ideal.script.dto.ParameterValidationDto;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 启动常用任务API接口DTO
 *
 * <AUTHOR> Assistant
 */
public class StartCommonTaskDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 常用任务ID
     */
    @NotNull
    @Min(1)
    private Long commonTaskId;

    /**
     * 审核人ID（风险脚本必填，白名单脚本可为空）
     */
    @Min(1)
    private Long auditUserId;
    /**
     * 启动参数
     */
    @Valid
    private List<ParameterValidationDto> params;

    public List<ParameterValidationDto> getParams() {
        return params;
    }

    public void setParams(List<ParameterValidationDto> params) {
        this.params = params;
    }

    public Long getCommonTaskId() {
        return commonTaskId;
    }

    public void setCommonTaskId(Long commonTaskId) {
        this.commonTaskId = commonTaskId;
    }

    public Long getAuditUserId() {
        return auditUserId;
    }

    public void setAuditUserId(Long auditUserId) {
        this.auditUserId = auditUserId;
    }

    @Override
    public String toString() {
        return "StartCommonTaskDto{" +
                "commonTaskId=" + commonTaskId +
                ", auditUserId=" + auditUserId +
                '}';
    }
}
