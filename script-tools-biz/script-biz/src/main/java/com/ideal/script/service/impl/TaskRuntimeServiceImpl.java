package com.ideal.script.service.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.PageDataUtil;
import com.ideal.common.util.spring.SpringUtil;
import com.ideal.sc.CustomerProperty;
import com.ideal.sc.constants.CustomerConstants;
import com.ideal.script.common.constant.Constants;
import com.ideal.script.common.constant.enums.Enums;
import com.ideal.script.common.util.TransactionSyncUtil;
import com.ideal.script.dto.RetryScriptInstanceApiDto;
import com.ideal.script.dto.TaskRuntimeApiDto;
import com.ideal.script.dto.TaskRuntimeQueryApiDto;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.mapper.TaskRuntimeMapper;
import com.ideal.script.mapper.TaskRuntimeStdoutMapper;
import com.ideal.script.model.bean.TaskHandleParam;
import com.ideal.script.model.bean.TaskRunTimeBindAgentBean;
import com.ideal.script.model.dto.TaskInstanceDto;
import com.ideal.script.model.dto.TaskRuntimeDto;
import com.ideal.script.model.dto.TaskStartDto;
import com.ideal.script.model.entity.TaskRuntime;
import com.ideal.script.model.entity.TaskRuntimeStdout;
import com.ideal.script.observer.itsm.ItsmScriptTaskResultPush;
import com.ideal.script.observer.numerical.ScheduledTaskNumericalResultPush;
import com.ideal.script.service.IAfterRuntimeHandlerService;
import com.ideal.script.service.IExectimeService;
import com.ideal.script.service.IScriptTaskStatePublisher;
import com.ideal.script.service.ITaskExecuteService;
import com.ideal.script.service.ITaskInstanceService;
import com.ideal.script.service.ITaskRuntimeService;
import com.ideal.script.service.ITaskRuntimeStdoutService;
import com.ideal.system.common.component.model.CurrentUser;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.core.Set;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RQueue;
import org.redisson.api.RScript;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * agent运行实例Service业务层处理
 *
 * <AUTHOR>
 */
@Service
public class TaskRuntimeServiceImpl implements ITaskRuntimeService {

    private static final Logger logger = LoggerFactory.getLogger(TaskRuntimeServiceImpl.class);
    private final TaskRuntimeMapper taskRuntimeMapper;

    private final TaskRuntimeStdoutMapper taskRuntimeStdoutMapper;

    private final IExectimeService exectimeService;

    private final RedissonClient redissonClient;

    private final ITaskExecuteService taskExecuteService;

    private final ItsmScriptTaskResultPush itsmScriptTaskResultPush;

    private final ScheduledTaskNumericalResultPush scheduledTaskNumericalResultPush;

    private final IScriptTaskStatePublisher scriptTaskStatePublisher;

    private final CustomerProperty customerProperty;

    private final ITaskInstanceService taskInstanceService;

    private final ITaskRuntimeStdoutService taskRuntimeStdoutService;

    public TaskRuntimeServiceImpl(ScheduledTaskNumericalResultPush scheduledTaskNumericalResultPush, TaskRuntimeMapper taskRuntimeMapper, IExectimeService exectimeService, TaskRuntimeStdoutMapper taskRuntimeStdoutMapper, RedissonClient redissonClient, @Lazy ITaskExecuteService taskExecuteService, @Lazy ItsmScriptTaskResultPush itsmScriptTaskResultPush, IScriptTaskStatePublisher scriptTaskStatePublisher, CustomerProperty customerProperty, ITaskInstanceService taskInstanceService, ITaskRuntimeStdoutService taskRuntimeStdoutService) {
        this.scheduledTaskNumericalResultPush = scheduledTaskNumericalResultPush;
        this.taskRuntimeMapper = taskRuntimeMapper;
        this.exectimeService = exectimeService;
        this.taskRuntimeStdoutMapper = taskRuntimeStdoutMapper;
        this.redissonClient = redissonClient;
        this.taskExecuteService = taskExecuteService;
        this.itsmScriptTaskResultPush = itsmScriptTaskResultPush;
        this.scriptTaskStatePublisher = scriptTaskStatePublisher;
        this.customerProperty = customerProperty;
        this.taskInstanceService = taskInstanceService;
        this.taskRuntimeStdoutService = taskRuntimeStdoutService;
    }

    /**
     * 查询agent运行实例
     *
     * @param id agent运行实例主键
     * @return agent运行实例
     */
    @Override
    public TaskRuntimeDto selectTaskRuntimeById(Long id) {
        return BeanUtils.copy(taskRuntimeMapper.selectTaskRuntimeById(id), TaskRuntimeDto.class);
    }

    /**
     * 查询agent运行实例列表
     *
     * @param taskRuntimeDto agent运行实例
     * @return agent运行实例
     */
    @Override
    public PageInfo<TaskRuntimeDto> selectTaskRuntimeList(TaskRuntimeDto taskRuntimeDto, int pageNum, int pageSize) {
        PageMethod.startPage(pageNum, pageSize);
        List<TaskRuntime> taskRuntimeList = taskRuntimeMapper.selectTaskRuntimeList(BeanUtils.copy(taskRuntimeDto, TaskRuntime.class));
        //遍历查询的数据，用系统当前时间减去agent实例开始时间，如果大于设定的超时时间，则标记为超时
        long systemTime = System.currentTimeMillis();
        for(TaskRuntime taskRuntime : taskRuntimeList){
            long endTime = taskRuntime.getEndTime() == null ? systemTime : taskRuntime.getEndTime().getTime();
            Long timeoutValue = taskRuntime.getTimeoutValue();
            if(Objects.nonNull(timeoutValue) && timeoutValue > 0 && (endTime - taskRuntime.getStartTime().getTime()) > timeoutValue * 1000){
                taskRuntime.setTimeout(1);
            }else{
                taskRuntime.setTimeout(0);
            }
        }
        return PageDataUtil.toDtoPage(taskRuntimeList, TaskRuntimeDto.class);
    }

    @Override
    public PageInfo<TaskRuntimeApiDto> getTaskRuntimeInfoByInstanceId(TaskRuntimeQueryApiDto taskRuntimeQueryApiDto) {
        PageMethod.startPage(taskRuntimeQueryApiDto.getPageNum(), taskRuntimeQueryApiDto.getPageSize());
        return PageDataUtil.toDtoPage(taskRuntimeMapper.selectTaskRuntimeList(BeanUtils.copy(taskRuntimeQueryApiDto, TaskRuntime.class)), TaskRuntimeApiDto.class);
    }
    /**
     * 驱动下一批次agent方法，TODO 暂时先使用开启新事务的方式，不开新事务的话得重构大改才行
     * @param taskRuntimeId agent实例id
     * @throws ScriptException 抛出自定义通知异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRES_NEW)
    public void driverNextBatchAgent(Long taskRuntimeId) throws ScriptException {
        try {

            //根据taskRunTimeId获取agent实例信息
            TaskRuntime taskRuntime = taskRuntimeMapper.selectTaskRuntimeById(taskRuntimeId);

            Map<String, Object> map = getTaskInfoFromRedis(taskRuntime.getTaskInstanceId());
            if (map == null || map.isEmpty()){
                return;
            }
            String userStr = (String) map.get(Constants.USER);
            String taskStartDtoStr = (String) map.get(Constants.TASK_START_DTO);
            String eachNum = (String) map.get(Constants.EACH_NUM);
            String driveMode = (String) map.get(Constants.DRIVER_MODE);
            String total = (String) map.get(Constants.TOTAL);
            //选择性执行模式，不驱动
            if(Integer.parseInt(driveMode) == Enums.DriverModel.SELECTIVE.getValue()){
                return;
            }
            //计数器当前值
            long nowCount = (long) map.get(Constants.NOW_COUNT);

            //获取redis hash中的dto数据
            TaskStartDto taskStartDto = JSON.parseObject(taskStartDtoStr, TaskStartDto.class);
            //任务实例id赋值
            taskStartDto.setIscriptTaskInstanceId(taskRuntime.getTaskInstanceId());

            // TODO 后续有时间优化新增接口，触发任务完成相应逻辑
            // 任务完成触发的业务逻辑
            if (0 == nowCount) {
                //更新任务实例表状态
                taskInstanceService.updateTaskInstanceState(taskRuntime.getTaskInstanceId());
                //任务完成推送itsm
                itsmScriptTaskResultPush.pushMessage(taskRuntimeId, "【finish】任务执行完毕", false);
                //任务完成发送统计短信
                scheduledTaskNumericalResultPush.pushMessage(taskRuntimeId);
                //中信作业中心监控，任务完成消息推送
                if (Objects.equals(customerProperty.getName(), CustomerConstants.CITIC)) {
                    scriptTaskStatePublisher.publish(taskStartDto, Enums.TaskInstanceStatus.COMPLETED);
                }
                return;
            }
            if (!ObjectUtils.notEqual(taskStartDto.getDriveMode(), null)) {
                taskStartDto.setDriveMode(Integer.valueOf(driveMode));
            }
            //（agent总数 - 当前剩余未执行的agent）除以 并发数，取余如果是0，说明本批次执行完了，应该拉起下一批
            // 如果结果不为0，说明本批次没有执行结束，直接返回
            // 只适用于分批、忽略异常分批模式，队列不需要判断
            if (Set.of(Enums.DriverModel.BATCH_EXEC.getValue(), Enums.DriverModel.IGNORE_ERROR_BATCH_EXEC.getValue()).contains(taskStartDto.getDriveMode())) {
                if (0 != ((Integer.parseInt(total) - nowCount) % Integer.parseInt(eachNum))) {
                    return;
                }
            }
            //走到这里，说明再次执行的agent不是首批了，这个布尔值在驱动方法里判断使用，例如不再单独插入instance表数据
            taskStartDto.setFirstBatch(false);
            //所有agent都是一个instanceId，这里直接复用
            taskStartDto.setIscriptTaskInstanceId(taskRuntime.getTaskInstanceId());
            //用户数据
            CurrentUser user = JSON.parseObject(userStr, CurrentUser.class);
            //队列模式
            //需要从redis队列中获取一个已经保存的ipsId作为下一次驱动的agent
            if (taskStartDto.getDriveMode().equals(Enums.DriverModel.QUEUE_EXEC.getValue())) {
                RQueue<Long> redisQueue = redissonClient.getQueue(String.format(Constants.SCRIPT_TASK_EXEC_REDIS_QUEUE_KEY, taskStartDto.getIscriptTaskInstanceId()));
                Long ipsId = redisQueue.poll();
                if (ObjectUtils.notEqual(ipsId, null)) {
                    Long[] ipsIds = {ipsId};
                    taskStartDto.setTaskIps(ipsIds);
                    //拉起下一批
                    taskExecuteService.scriptTaskStart(taskStartDto, user);
                }
            } else
                //分批模式
                if (Enums.DriverModel.BATCH_EXEC.getValue().toString().equals(map.get(Constants.DRIVER_MODE))) {
                    //判断，如果本次任务有异常\运行的agent，不拉起下一批
                    List<TaskRuntime> taskRuntimes = taskRuntimeMapper.selectErrorRuntimeList(taskRuntime);
                    if (!taskRuntimes.isEmpty()) {
                        return;
                    }
                    //拉起下一批
                    taskExecuteService.scriptTaskStart(taskStartDto, user);
                } else
                    //忽略异常分批直接拉起下一批
                    if (Enums.DriverModel.IGNORE_ERROR_BATCH_EXEC.getValue().toString().equals(map.get(Constants.DRIVER_MODE))) {
                        //拉起下一批
                        taskExecuteService.scriptTaskStart(taskStartDto, user);
                    }

        }catch (ScriptException ex){
            throw new ScriptException("driverNextBatchAgent is error",ex);
        }
    }

    private Map<String, Object> getTaskInfoFromRedis(Long  taskInstanceId) {
        //获取redis数据，1、hash内容，里面保存了total、eachNum等数据；2、计数器数值
        String taskKey = String.format(Constants.SCRIPT_TASK_EXEC_REDIS_KEY, taskInstanceId);
        String counterKey = String.format(Constants.SCRIPT_TASK_EXEC_REDIS_COUNTER_KEY, taskInstanceId);
            /*
              获取任务的计数器、任务信息hash数据，并递减计数器，同时返回hash中的所有数据
              KEYS[1]: script-task-code-flag-{taskId}  (hash结构)
              KEYS[2]: script-task-code-flag-{taskId}-total  (string结构，计数器)
              ARGV[1]: user field
              ARGV[2]: taskStartDto field
              ARGV[3]: eachNum field
              ARGV[4]: driveMode field
              ARGV[5]: total field
             */
        String luaScript =
                "if redis.call('EXISTS', KEYS[1]) == 0 or redis.call('EXISTS', KEYS[2]) == 0 then\n" +
                "    return {}\n" +
                "end\n" +
                "local nowCount = redis.call('DECR', KEYS[2])\n" +
                "local hashData = redis.call('HMGET', KEYS[1], unpack(ARGV))\n" +
                "table.insert(hashData, nowCount)\n" +
                "if nowCount == 0 then\n" +
                "    redis.call('DEL', KEYS[1])\n" +
                "    redis.call('DEL', KEYS[2])\n" +
                "end\n" +
                "return hashData";


        List<Object> keys = Arrays.asList(taskKey, counterKey);
        List<Object> args = new ArrayList<>(Arrays.asList(Constants.USER, Constants.TASK_START_DTO, Constants.EACH_NUM,
                                     Constants.DRIVER_MODE, Constants.TOTAL));
        List<Object> result = redissonClient.getScript().eval(RScript.Mode.READ_WRITE, luaScript, RScript.ReturnType.MULTI, keys,args.toArray());
        if (CollectionUtils.isEmpty(result)) {
            logger.warn("脚本服务化任务实例id:{}，不存在相关redis数据，可能任务已经执行完毕。", taskInstanceId);
            return null;
        }
        // lua脚本中增加了此值，所以需要增加
        args.add(Constants.NOW_COUNT);
        return IntStream.range(0, args.size())
                        .boxed()
                        .collect(Collectors.toMap(i -> args.get(i).toString(), result::get
                        ));
    }

    /**
     * 新增agent运行实例
     *
     * @param taskRuntimeDto agent运行实例
     * @return 结果
     */
    @Override
    public int insertTaskRuntime(TaskRuntimeDto taskRuntimeDto) {
        TaskRuntime taskRuntime = BeanUtils.copy(taskRuntimeDto, TaskRuntime.class);
        return taskRuntimeMapper.insertTaskRuntime(taskRuntime);
    }

    /**
     * 修改agent运行实例
     *
     * @param taskRuntimeDto agent运行实例
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateTaskRuntime(TaskRuntimeDto taskRuntimeDto) {
        TaskRuntime taskRuntime = BeanUtils.copy(taskRuntimeDto, TaskRuntime.class);
        return taskRuntimeMapper.updateTaskRuntime(taskRuntime);
    }

    /**
     * 批量删除agent运行实例
     *
     * @param ids 需要删除的agent运行实例主键
     * @return 结果
     */
    @Override
    public int deleteTaskRuntimeByIds(Long[] ids) {
        return taskRuntimeMapper.deleteTaskRuntimeByIds(ids);
    }

    /**
     * 删除agent运行实例信息
     *
     * @param id agent运行实例主键
     * @return 结果
     */
    @Override
    public int deleteTaskRuntimeById(Long id) {
        return taskRuntimeMapper.deleteTaskRuntimeById(id);
    }

    /**
     * 功能描述： 更新agent状态
     *
     * @param status 状态
     * @param notInStates 排除的状态
     * @param taskRuntimeId agent运行实例Id
     * <AUTHOR>
     */
    @Override
    public void updateTaskRuntimeState(int status, List<Integer> notInStates, Long taskRuntimeId) {
        TaskRuntime taskRuntime = taskRuntimeMapper.getTaskRuntimeStartTime(taskRuntimeId);
        taskRuntimeMapper.updateTaskRuntimeState(status, notInStates, taskRuntimeId, taskRuntime.getEndTime(), (taskRuntime.getEndTime().getTime() - taskRuntime.getStartTime().getTime()) / 1000);
    }

    @Override
    public String getOutPutMessage(Long id) throws ScriptException {
        String stdout = "";
        try {
            //根据taskRuntimeId获取agent标准输出
            TaskRuntimeStdout taskRuntimeStdout = taskRuntimeStdoutMapper.selectStdoutByTaskRuntimeId(id);
            if(ObjectUtils.notEqual(taskRuntimeStdout,null)){
                stdout = taskRuntimeStdout.getIstdout() + taskRuntimeStdout.getIstderror();
            }
        } catch (Exception e) {
            logger.error("get out put result fail!",e);
            throw new ScriptException("get.out.put.result.fail");
        }
        return stdout;
    }

    /**
     * 功能描述：根据 Agent 实例 ID 查询当前任务中除自身外其他 Agent 执行出现异常的情况
     * 确定当前任务中除自身外其他 Agent 是否出现了执行异常的情况
     *
     * @param id agent实例主键
     * @return {@link Integer }
     * <AUTHOR>
     */
    @Override
    public Integer selectCountByTaskInstanceId(Long id) {
        return taskRuntimeMapper.selectCountByTaskInstanceId(id);
    }

    /**
     * 获取agent实例数据
     * @param retryScriptInstanceApiDto 参数
     * @return agent实例对象
     */
    @Override
    public TaskRuntime getTaskRuntime(RetryScriptInstanceApiDto retryScriptInstanceApiDto){
        return taskRuntimeMapper.getTaskRuntime(retryScriptInstanceApiDto);
    }

    /**
     * 功能描述：根据agent实例主键查询运行中的agent数量
     *
     * @param id agent实例主键
     * @return {@link Integer }
     * <AUTHOR>
     */
    @Override
    public Integer getRunningAgentInstanceCount(Long id) {
        return taskRuntimeMapper.getRunningAgentInstanceCount(id);
    }

    /**
     * 功能描述：根据agent实例主键获取绑定的agent信息
     *
     * @param taskRuntimeId agent实例主键
     * @return {@link TaskRunTimeBindAgentBean }
     * <AUTHOR>
     */
    @Override
    public TaskRunTimeBindAgentBean getBindAgentForTaskRuntime(Long taskRuntimeId) {
        return taskRuntimeMapper.getBindAgentForTaskRuntime(taskRuntimeId);
    }

    /**
     * 功能描述：根据agent运行实例主键获取agent信息
     *
     * @param taskRuntimeId  agent实例主键
     * @param status         状态
     * @param taskRuntimeDto agent运行实例对象
     */
    @Override
    public void updateExecTimeAndState(String taskRuntimeId, int status, TaskRuntimeDto taskRuntimeDto) {
        //更新执行次数 TODO 后续改成定时计算统计，不实时更新
        exectimeService.updateScriptExecTime(status, taskRuntimeDto.getSrcScriptUuid(),null);

        // 更新agent执行状态
        List<Integer> notInStates = new ArrayList<>();
        notInStates.add(Enums.TaskRuntimeState.TERMINATED.getValue());
        notInStates.add(Enums.TaskRuntimeState.SKIP.getValue());
        notInStates.add(Enums.TaskRuntimeState.COMPLETED.getValue());
        updateTaskRuntimeState(status, notInStates, Long.parseLong(taskRuntimeId));
    }

    /**
     * 获取agent实例标准输出
     * @param retryScriptInstanceApiDto 参数
     * @return 标准输出
     */
    @Override
    public String getAgentStdoutByTaskIdAndAddress(RetryScriptInstanceApiDto retryScriptInstanceApiDto){
        TaskRuntime taskRuntime = taskExecuteService.getTaskRuntime(retryScriptInstanceApiDto);
        return taskExecuteService.getRealTimeOutPutMessage(taskRuntime.getId());
    }

    /**
     * 根据agent地址、任务实例id  查询agent实例id集合
     * @param agentAddressList agent地址集合
     * @param taskInstanceId 任务实例id
     * @return agent实例id集合
     */
    @Override
    public List<Long> selectRuntimeIdsByAgentAddressAndTaskInstanceId(List<String> agentAddressList, Long taskInstanceId){
        return taskRuntimeMapper.selectRuntimeIdsByAgentAddressAndTaskInstanceId(agentAddressList, taskInstanceId);
    }

    /**
     * 根据任务实例id获取对应的agent实例数据
     * @param taskInstanceId 任务实例数据
     * @return agent实例对象集合
     */
    @Override
    public List<TaskRuntimeDto> getTaskRuntimeByInstanceId(Long taskInstanceId){
        return BeanUtils.copy(taskRuntimeMapper.getTaskRuntimeByInstanceId(taskInstanceId),TaskRuntimeDto.class);
    }
    /**
     * 处理脚本执行结果更新runtime表状态
     *
     * @param taskRuntimeId   agent运行实例id
     * @param status          状态
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateScriptTaskRuntime(String taskRuntimeId, int status)  {
        // 执行后续操作
        TaskRuntimeDto taskRuntimeDto = selectTaskRuntimeById(Long.parseLong(taskRuntimeId));
        if (Objects.nonNull(taskRuntimeDto) && Objects.nonNull(taskRuntimeDto.getTaskInstanceId()) && null != taskRuntimeDto.getState()) {
            int oldStatus = taskRuntimeDto.getState();
            // 运行中才更新runtime表
            if (Enums.TaskRuntimeState.RUNNING.getValue() == oldStatus) {
                //更新agent实例状态
                SpringUtil.getBean(ITaskRuntimeService.class).updateExecTimeAndState(taskRuntimeId, status, taskRuntimeDto);
                //当任务实例状态不是完成并且agent结果是异常状态则更新任务实例状态为异常。
                if (Enums.TaskRuntimeState.SCRIPT_FAIL_STATE.getValue() == status) {
                    int count = taskInstanceService.updateState(Enums.TaskInstanceStatus.EXCEPTION.getValue(), taskRuntimeDto.getTaskInstanceId(),Constants.SCRIPT_FINISH_SET);
                    if(count == 0){
                        logger.warn("The status of the task instance has already been in an exception state,taskInstanceId: {}", taskRuntimeDto.getTaskInstanceId());
                    }
                }
            } else {
                logger.warn("taskRuntime is processed, skipping.{}", taskRuntimeId);
            }
        }
    }
    /**
     *  消费mq后，处理脚本标准输出、是否驱动下一批次，其他模块发送mq等
     * @param taskRuntimeId   agent运行实例id
     * @param status          agent实例状态
     * @param taskHandleParam 任务处理参数对象
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleScriptExecuteResult(String taskRuntimeId, int status, TaskHandleParam taskHandleParam) throws ScriptException {
        ITaskRuntimeService taskRuntimeService = SpringUtil.getBean(ITaskRuntimeService.class);
        TaskRuntimeDto taskRuntimeDto = taskRuntimeService.selectTaskRuntimeById(Long.parseLong(taskRuntimeId));
        String bizId = taskRuntimeDto.getBizId();
        if (StringUtils.isNotBlank(bizId)) {
            //数据库中的状态是运行中才更新runtime表
            //如果是重试操作，执行更新标准输出数据、更新任务状态
            if(Enums.TaskRuntimeState.RUNNING.getValue() == taskRuntimeDto.getState()){
                //保存标准输出、错误输出
                taskRuntimeStdoutService.saveRuntimeStdoutStdError(taskHandleParam, bizId, taskRuntimeDto);
                //更新agent实例runtime表及任务实例instance表
                taskRuntimeService.updateScriptTaskRuntime(taskRuntimeId, status);
                //启动情况才驱动
                if(bizId.startsWith(Enums.AgentExecRunFlag.START.getValue())){
                    //开启新事务 调用驱动下一批次
                    TransactionSyncUtil.execute(taskRuntimeService::driverNextBatchAgent,null,null,Long.parseLong(taskRuntimeId));
                }
                //重试情况特殊处理
                if(bizId.startsWith(Enums.AgentExecRunFlag.RETRY.getValue())) {
                    //这里是兜底操作,计数器不存在，可能是所有agent已经跑过一次的情况，此次返回结果应该是点了其中某一个节点进行的重试
                    RAtomicLong taskCounter = redissonClient.getAtomicLong(String.format(Constants.SCRIPT_TASK_EXEC_REDIS_COUNTER_KEY, taskRuntimeDto.getTaskInstanceId()));
                    if (!taskCounter.isExists()) {
                        taskInstanceService.updateTaskInstanceState(taskRuntimeDto.getTaskInstanceId());
                    }
                }

                TaskInstanceDto taskInstanceByRuntimeId = taskInstanceService.getTaskInstanceByRuntimeId(Long.parseLong(taskRuntimeId));
                //定时任务启动的脚本任务发送正常推送mq，如果工具箱、脚本任务有什么特殊处理再加对应的接口实现类
                if(Enums.ScriptStartType.TIMETASK.getStartValue().equals(taskInstanceByRuntimeId.getStartType().toString())){
                    String cronTabsTopic = Constants.CRONTABS_EXECUTE_RESULT_DEV;
                    if(Enums.TaskRuntimeState.SCRIPT_FAIL_STATE.getValue() == status){
                        cronTabsTopic = Constants.CRONTABS_ERROR_RESULT_DEV;
                    }
                    IAfterRuntimeHandlerService bean = SpringUtil.getBean(Enums.ScriptStartType.getValueByStartValue(taskInstanceByRuntimeId.getStartType().toString()));
                    bean.monitorMqToSend(cronTabsTopic,taskHandleParam,taskRuntimeDto);
                }

            }else { // processed
                logger.warn("taskRuntimeId {} has been processed,received status: {}, skipping.", taskRuntimeId,status);
            }
        }else{
            logger.error("taskRuntimeId {} bizId is null, skipping.", taskRuntimeId);
            throw new ScriptException("bizId is null");
        }
    }


}
