package com.ideal.script.model.dto;

import java.io.Serializable;

/**
 * agent实例终止dto
 * <AUTHOR>
 */
@SuppressWarnings("unused")
public class ScriptStopShellDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * agent实例id
     */
    private Long [] runTimeIds;

    /**
     * 任务id
     */
    private Long taskId;

    /**
     * 任务实例id
     */
    private Long taskInstanceId;

    /**
     * agent地址，格式（ip:端口）
     */
    private String [] agentAddress;

    public Long[] getRunTimeIds() {
        return runTimeIds;
    }

    public void setRunTimeIds(Long[] runTimeIds) {
        this.runTimeIds = runTimeIds;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public Long getTaskInstanceId() {
        return taskInstanceId;
    }

    public void setTaskInstanceId(Long taskInstanceId) {
        this.taskInstanceId = taskInstanceId;
    }

    public String[] getAgentAddress() {
        return agentAddress;
    }

    public void setAgentAddress(String[] agentAddress) {
        this.agentAddress = agentAddress;
    }
}
