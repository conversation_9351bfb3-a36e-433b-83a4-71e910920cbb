package com.ideal.script.model.dto;

import java.io.Serializable;

/**
 * 双人复核对接所需信息Dto
 * <AUTHOR>
 */
public class DoubleCheckInfoDto implements Serializable {
    /**
     * 任务的id
     */
    private Long taskId;

    /**
     * 审核人
     */
    private String auditUser;

    /**
     * 审核人Id
     */
    private Long auditUserId;

    /**
     * 业务模块类型
     */
    private String itemType;
    /**
     * 回调接口URL
     */
    private String callbackUrl;

    /**
     * 判断重复标识
     */
    private String uuid;
    /**
     * 双人复核脚本关系表主键id
     */
    private Long auditRelationId;
    /**
     * 任务主题
     */
    private String taskSubject;
    /**
     * 申请人id
     */
    private Long originatorId;
    /**
     * 申请人名称
     */
    private String originatorName;
    /**
     * 审核详情页面URL
     */
    private String detailUrl;

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public String getDetailUrl() {
        return detailUrl;
    }

    public void setDetailUrl(String detailUrl) {
        this.detailUrl = detailUrl;
    }

    public String getAuditUser() {
        return auditUser;
    }

    public void setAuditUser(String auditUser) {
        this.auditUser = auditUser;
    }

    public Long getAuditUserId() {
        return auditUserId;
    }

    public void setAuditUserId(Long auditUserId) {
        this.auditUserId = auditUserId;
    }

    public String getItemType() {
        return itemType;
    }

    public void setItemType(String itemType) {
        this.itemType = itemType;
    }

    public String getCallbackUrl() {
        return callbackUrl;
    }

    public void setCallbackUrl(String callbackUrl) {
        this.callbackUrl = callbackUrl;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public Long getAuditRelationId() {
        return auditRelationId;
    }

    public void setAuditRelationId(Long auditRelationId) {
        this.auditRelationId = auditRelationId;
    }

    public String getTaskSubject() {
        return taskSubject;
    }

    public void setTaskSubject(String taskSubject) {
        this.taskSubject = taskSubject;
    }

    public Long getOriginatorId() {
        return originatorId;
    }

    public void setOriginatorId(Long originatorId) {
        this.originatorId = originatorId;
    }

    public String getOriginatorName() {
        return originatorName;
    }

    public void setOriginatorName(String originatorName) {
        this.originatorName = originatorName;
    }
}
