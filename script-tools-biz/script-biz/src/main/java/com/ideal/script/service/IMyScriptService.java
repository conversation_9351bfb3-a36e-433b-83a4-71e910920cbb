package com.ideal.script.service;

import com.github.pagehelper.PageInfo;
import com.ideal.approval.dto.DoubleCheckApiDto;
import com.ideal.script.dto.PublishDto;
import com.ideal.script.dto.ScriptDubboInfoDto;
import com.ideal.script.dto.ScriptInfoApiDto;
import com.ideal.script.dto.ScriptInfoDto;
import com.ideal.script.dto.ScriptInfoQueryDto;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.exception.SystemException;
import com.ideal.script.model.bean.AuditResultBean;
import com.ideal.script.model.bean.DownloadScriptBean;
import com.ideal.script.model.bean.ExecutorValidation;
import com.ideal.script.model.bean.MyScriptBean;
import com.ideal.script.model.dto.*;
import com.ideal.script.model.entity.Attachment;
import com.ideal.system.common.component.model.CurrentUser;
import com.ideal.system.dto.UserInfoApiDto;
import com.ideal.system.dto.UserInfoQueryDto;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface IMyScriptService {

    /**
     * 保存
     *
     * @param scriptInfoDto dto
     * @return long
     * @throws ScriptException exception
     */
    ValidationResultDto saveScript(ScriptInfoDto scriptInfoDto) throws ScriptException;

    /**
     * 更新
     *
     * @param scriptInfoDto dto
     * @return long
     * @throws ScriptException 脚本服务化自定义异常
     */
    ValidationResultDto updateMyScript(ScriptInfoDto scriptInfoDto) throws ScriptException;

    /**
     * 查询脚本列表(分页)
     *
     * @param scriptInfoQueryDto dto
     * @return pageInfo
     */
    PageInfo<ScriptInfoApiDto> selectScriptPageList(ScriptInfoQueryDto scriptInfoQueryDto);

    /**
     * 查询脚本列表
     * @param scriptInfoQueryDto    脚本查询条件信息Dto
     * @return  脚本列表
     */
    List<ScriptInfoApiDto> selectScriptList(ScriptInfoQueryDto scriptInfoQueryDto);

    /**
     * 查询
     *
     * @param scriptInfoQueryDto 脚本查询信息
     * @return ScriptEditDto
     */
    ScriptInfoDto getScriptDetail(ScriptInfoQueryDto scriptInfoQueryDto) throws ScriptException;

    /**
     * 删除
     *
     * @param scriptDeleteDto       删除脚本所需信息
     * @param highPower 为true 可删除发布的脚本
     * @throws ScriptException exception
     */
    void deleteMyScript(ScriptDeleteDto scriptDeleteDto,boolean highPower) throws ScriptException;

    /**
     * 发布
     *
     * @param publishDto 发布信息Dto
     * @throws ScriptException  ScriptException
     */
    void publishScript(PublishDto publishDto) throws ScriptException;

    /**
     * 脚本自动发布，跳过双人复核，目前仅为工具箱提供
     *
     * @param publishDto 发布信息Dto
     * @throws ScriptException  ScriptException
     */
    void publishScriptAuto(PublishDto publishDto) throws ScriptException;

    /**
     * 根据scriptInfoVersionId获取脚本发布的审核状态
     * @param scriptInfoVersionId 脚本的id
     * @return 是否存在已经发布信息标识
     * @throws ScriptException 脚本服务化异常
     */
    boolean getDoubleCheckScriptFlag(Long [] scriptInfoVersionId);

    /**
     * 根据relationId查询是否为双人复核打回的删除请求
     * @param scriptRelationId relationId
     * @return 是否为删除的请求
     * @throws ScriptException 脚本服务化异常
     */
    boolean getDelDoubleCheckScriptFlag(Long scriptRelationId) throws ScriptException;
    /**
     * 处理双人复核审核脚本结果
     * @param auditResultBean 复核结果Bean对象
     * @throws SystemException 抛出自定义异常
     */
    void updateScriptReview(AuditResultBean auditResultBean) throws SystemException;

    /**
     * 处理双人复核审核脚本删除的结果
     * @param auditResultBean 复核结果Bean对象
     * @throws SystemException 抛出自定义异常
     */
    void updateScriptReviewForDelete(AuditResultBean auditResultBean) throws SystemException;

    /**
     * 下载脚本
     *
     * @param ids      脚本id
     * @param response 响应
     * @return 成功|失败
     */
    boolean downloadScript(Long[] ids, HttpServletResponse response);


    /**
     * 查询
     *
     * @param serviceUuid 脚本的uuid
     * @return 列表
     */
    List<ScriptVersionInfoDto> getScriptServiceVersionListForAllScript(String serviceUuid);

    /**
     * 版本回退（最新脚本无版本）
     *
     * @param iid     回退到的版本的主键
     * @param oldId   回退的版本的主键
     * @param oldUuid 回退到的版本的uuid
     * @param uuid    回退的版本的uuid
     * @return boolean
     */
    Map<String,Object> noVersionRollBack(Long iid, Long oldId, String oldUuid, String uuid);

    /**
     * 版本回退（最新脚本有版本）
     *
     * @param iid   回退到的版本的主键
     * @param oldId 回退的版本的主键
     * @return 布尔
     */
    Map<String,Object> hasVersionRollBack(Long iid, Long oldId);


    /**
     * 获取下载列表基础信息和附件信息
     *
     * @param iid 版本id
     * @return 列表
     */
    List<DownloadScriptBean> downloadScriptInfo(Long[] iid);

    /**
     * 创建文件
     *
     * @param fileName       文件名
     * @param scriptContent  脚本内容
     * @param attachmentList 附件列表
     * @param path           路径
     * @return 文件
     */
    File createFile(String fileName, String scriptContent, List<Attachment> attachmentList, String path, boolean createScriptFiles) throws ScriptException;

    /**
     * 接收审核结果并处理
     *
     * @param doubleCheckApiDto 审核服务反馈的审批结果
     * @param typeValue 审核服务反馈的处理类型
     * @throws SystemException 抛出自定义异常
     */
    void doubleCheckScriptCallBack(DoubleCheckApiDto doubleCheckApiDto, Integer typeValue) throws SystemException;


    /**
     * 校验参数名称是否重复
     *
     * @param scriptInfoDto 参数集合
     * @return 判断值
     */
    boolean paramsNameExistCheck(ScriptInfoDto scriptInfoDto);
    /**
     * 创建者转移获取用户管理中所有用户 分页
     * @param userInfoQueryDto 用户查询条件
     * @return PageInfo<UserInfoApiDto>
     */
    PageInfo<UserInfoApiDto> getUserInfoList(UserInfoQueryDto userInfoQueryDto);
    /**
     * 创建者转移
     * @param creatorTransferDto 参数（脚本Info的uuid,用户）
     */
    void creatorTransfer(CreatorTransferDto creatorTransferDto);

    /**
     * 根据脚本id获取当前默认版本id
     * @param scriptIds 脚本id
     * @return Long []
     */
    Long [] getDefaultVersionIds(Long [] scriptIds);

    /**
     * 根据脚本任意的版本uuid查询默认版本的脚本基本信息
     *
     * @param scriptInfoQueryDto 脚本信息查询条件
     * @return ScriptInfoDto
     * <AUTHOR>
     */
    ScriptDubboInfoDto getDefaultScriptInfoApi(ScriptInfoQueryDto scriptInfoQueryDto) throws ScriptException;


    ScriptDubboInfoDto getScriptDetailForDubbo(ScriptInfoQueryDto scriptInfoQueryDto) throws ScriptException;

    void ignoreScriptRevision(List<Long> ids, CurrentUser currentUser);

    /**
     * 推送告警
     * @param isrcScriptUuid 脚本的uuid
     * @param userId 用户id
     */
    void toAlarm(String isrcScriptUuid,Long userId);

    Set<String> getLabelList();

    ExecutorValidation getExecutorValidationList();

    /**
     * 脚本默认版本切换通知接口,当前是版本回退、脚本发布审核通过后调用
     * @param myScriptBean myScriptBean参数
     */
    void noticeScriptChangeDefaultVersion(MyScriptBean myScriptBean);

    /**
     * 获取sql脚本是否展示开关
     */
    boolean getSqlShowFlag();

    /**
     * 调用双人复核归档
     * @param scriptInfoVersionId 脚本的版本id
     */
    void doubleCheckAutoArchive(Long [] scriptInfoVersionId);

    /**
     * 获取发布脚本为白名单标识
     */
    boolean getPublishScriptWhiteFlag();

    /**
     * 获取是否走角色权限标识
     * @return 标识布尔值
     */
    boolean getRolePermissionFlag();

    /**
     * 获取当前查询权限
     * @return 权限布尔值
     */
    boolean getRolePermission();

    /**
     * 根据用户获取用户角色绑定的脚本分类路径
     * @param user 用户对象
     * @return 分类路径集合
     */
    List<String> getCategoryPathByUserRole(CurrentUser user);

    /**
     * 获取分类全路径
     * @param categoryId 分类id
     * @return 全路径
     */
    String getCategoryFullPath(Long categoryId);

    /**
     * 校验关键命令
     *
     * @param scriptInfoDto 脚本内容Dto
     * @return 列表
     */
    List<ScriptValidationResultDto> validateScriptWithKeywords(ScriptInfoDto scriptInfoDto);
}
